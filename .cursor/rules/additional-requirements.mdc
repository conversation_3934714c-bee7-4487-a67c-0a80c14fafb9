---
description: 其他额外规范要求
globs:
alwaysApply: true
---

# 其他额外规范要求

## 样式开发规范
### UnoCSS 优先原则
- **禁止使用 `<style>` 标签**：所有样式必须使用 UnoCSS 原子类实现
- **优先使用 UnoCSS 预定义变量**：
  - 颜色：使用 UnoCSS 预定义的颜色变量，如 `text-gray-600`、`bg-blue-500` 等
  - 间距：使用 UnoCSS 间距系统，如 `p-4`、`m-2`、`gap-4` 等
  - 字体：使用 UnoCSS 字体系统，如 `text-sm`、`font-medium` 等
  - 阴影：使用 UnoCSS 阴影系统，如 `shadow-sm`、`shadow-lg` 等
- **禁止自定义颜色值**：不允许使用自定义的十六进制颜色值或 RGB 值
- **响应式设计**：使用 UnoCSS 响应式前缀，如 `md:flex`、`lg:grid-cols-3` 等
- **状态样式**：使用 UnoCSS 状态前缀，如 `hover:bg-blue-600`、`focus:ring-2` 等

## 代码格式化
每次完成代码编写，务必执行代码格式化操作，可以不进行 eslint 修复，只需要执行命令即可
```sh
npx eslint "src/modules/**/*.{vue,ts}" --cache --fix && npx stylelint \"src/modules/**/*.{css,scss,vue}\" --cache --fix
```
