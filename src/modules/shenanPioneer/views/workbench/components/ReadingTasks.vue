<!--
  待阅事项组件
  显示用户的待阅任务列表，支持标签页切换
-->
<script setup lang="ts">
import type { MessageRemindItem } from '@shenanPioneer/api'
import type { TagProps } from 'element-plus'
import { fetchReadMessages, fetchUnreadMessages } from '@shenanPioneer/api'
import { EnumToOptions, FormatDate } from '@shencom/utils'
import { ElMessage } from 'element-plus'
import { MessageStatus, MessageType } from '@/modules/shenanPioneer/enum/message'
import { getDateRange } from '@/modules/shenanPioneer/utils/date'
import Card from './Card.vue'

/**
 * 待阅事项接口
 */
interface ReadingItem {
  /** 唯一标识 */
  id: string
  /** 日期 */
  date: string
  /** 标题 */
  title: string
  /** 状态 */
  type: MessageType
  /** 原始数据 */
  raw?: MessageRemindItem
}

const radioOptions = ref(EnumToOptions(MessageStatus))

const filterOptions = ref(['今日', '昨日', '全部'])

// ==================== 响应式数据 ====================
const loading = ref(false)
const activeTab = ref(MessageStatus.未读)
const selectedFilters = ref<string>('今日')
const readingList = ref<ReadingItem[]>([])
const totalCount = ref(0)

// ==================== 计算属性 ====================
const filteredReadingList = computed(() => {
  // 限制显示5条
  return readingList.value.slice(0, 5)
})

// ==================== 方法定义 ====================
/**
 * 转换消息数据为显示格式
 * @param items - 消息列表
 * @returns 转换后的数据
 */
function transformMessageData(items: MessageRemindItem[]): ReadingItem[] {
  return items.map((item) => {
    // 提取工程名称
    const titleMatch = item.content.match(/工程【(.+?)】/)
    const projectName = titleMatch ? titleMatch[1] : '未知工程'

    // 格式化日期
    const date = FormatDate(item.createdAt, 'MM-DD')

    return {
      id: item.id,
      date,
      title: `工程【${projectName}】`,
      type: item.type,
      raw: item,
    }
  })
}

/**
 * 获取消息数据
 */
async function fetchMessages() {
  loading.value = true
  try {
    const dateRange = getDateRange(selectedFilters.value)
    let response

    if (activeTab.value === MessageStatus.未读) {
      // 我的待阅
      response = await fetchUnreadMessages(dateRange)
    }
    else {
      // 我的已阅
      response = await fetchReadMessages(dateRange)
    }

    readingList.value = transformMessageData(response.content)
    totalCount.value = response.totalElements
  }
  catch (error) {
    console.error('获取消息数据失败:', error)
    ElMessage.error('获取消息数据失败')
    readingList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 处理阅读项点击
 * @param item - 阅读项
 */
function handleReadingClick(item: ReadingItem) {
  // 处理阅读项点击事件
  console.log('点击阅读项:', item)
}

/**
 * 处理更多点击
 */
function handleMoreClick() {
  // 处理更多点击
  console.log('点击更多')
}

/**
 * 处理标签页变化
 */
function handleTabChange() {
  // 重新获取数据
  fetchMessages()
}

/**
 * 获取类型标签类型
 * @param type - 类型
 * @returns 类型
 */
function getStatusType(type: MessageType): TagProps['type'] {
  switch (type) {
    case MessageType.已接入监管:
      return 'success'
    case MessageType.预约回收:
    case MessageType.预约安装:
      return 'primary'
    case MessageType.结束监管:
      return 'info'
    case MessageType.违规告警:
      return 'danger'
    default:
      return 'info'
  }
}

// ==================== 生命周期 ====================
onMounted(() => {
  fetchMessages()
})

// ==================== 监听器 ====================
watch(activeTab, () => {
  handleTabChange()
})

watch(selectedFilters, () => {
  // 时间过滤变化时重新获取数据
  fetchMessages()
})
</script>

<template>
  <Card title="待阅事项">
    <template #header>
      <el-radio-group v-model="selectedFilters" size="small" :disabled="loading">
        <el-radio-button v-for="filter in filterOptions" :key="filter" :label="filter">
          {{ filter }}
        </el-radio-button>
      </el-radio-group>
    </template>

    <div class="py-2">
      <el-radio-group v-model="activeTab" :disabled="loading">
        <el-radio-button
          v-for="item in radioOptions" :key="item.value" :label="item.value"
          size="small"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <div v-loading="loading" class="flex-1">
      <div class="h-full flex flex-col">
        <div class="flex-1">
          <div
            v-if="filteredReadingList.length === 0 && !loading"
            class="flex items-center justify-center py-8 text-gray-500"
          >
            暂无数据
          </div>
          <div
            v-for="item in filteredReadingList" :key="item.id"
            class="flex cursor-pointer items-center py-3 hover:text-primary" @click="handleReadingClick(item)"
          >
            <span class="mr-2 text-sm text-foreground/60">{{ item.date }}</span>
            <span class="flex-1 text-sm leading-relaxed ellipsis-1">{{ item.title }}</span>
            <ElTag v-if="item.type" :type="getStatusType(item.type)" size="small" class="ml-2">
              {{ MessageType[item.type] }}
            </ElTag>
          </div>
        </div>

        <div
          v-if="totalCount > 0"
          class="flex cursor-pointer items-center justify-center rounded p-2 text-sm text-blue-500 transition-colors duration-300 hover:bg-blue-50"
          @click="handleMoreClick"
        >
          <span class="mr-1">更多</span>
          <FaIcon name="i-ep:arrow-right" />
        </div>
      </div>
    </div>
  </Card>
</template>
