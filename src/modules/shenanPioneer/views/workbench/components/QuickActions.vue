<!--
  快捷功能组件
  显示常用功能的快捷入口，包括我的工程、告警列表、工程监控等
-->
<script setup lang="ts">
import Card from './Card.vue'

/**
 * 快捷功能项接口
 */
interface QuickItem {
  /** 唯一标识 */
  id: number
  /** 标签名称 */
  label: string
  /** 图标名称 */
  icon: string
  /** 路由地址 */
  route: string
}

// ==================== 路由实例 ====================
const router = useRouter()
const organizationStore = useOrganizationStore()

// ==================== 响应式数据 ====================
const quickData = computed(() => {
  return [
    { id: 1, label: '我的工程', icon: 'i-ep:folder', route: '/my-project' },
    { id: 2, label: '告警列表', icon: 'i-ep:warning', route: '/alarm-list' },
    { id: 3, label: '工程监控', icon: 'i-ep:video-camera', route: '/project-monitor' },
    organizationStore.currentOrganization ? { id: 4, label: '小散场景', icon: 'i-ep:grid', route: '/sporadic-scene' } : null,
    organizationStore.currentOrganization ? { id: 5, label: '统计分析', icon: 'i-ep:data-analysis', route: '/stat-analysis' } : null,
  ].filter(Boolean) as QuickItem[]
})

/**
 * 处理快捷功能点击
 * @param item - 快捷功能项
 */
function handleQuickAction(item: QuickItem): void {
  try {
    // 处理路由跳转
    if (item.route) {
      router.push(item.route)
    }
  }
  catch (error) {
    console.error('快捷功能跳转失败:', error)
  }
}
</script>

<template>
  <Card title="快捷功能">
    <div class="mt-4 flex flex-col justify-between gap-4 md:flex-row">
      <div
        v-for="item in quickData" :key="item.id" hover="bg-ui-primary/10 shadow-lg -translate-y-0.5"
        class="bg-ui-primary/5 w-full flex flex-col flex-1 cursor-pointer items-center rounded-lg p-4 transition-all duration-300 md:w-auto md:flex-auto"
        @click="handleQuickAction(item)"
      >
        <div class="mb-2 h-12 w-12 flex items-center justify-center rounded-lg bg-background text-blue-500 shadow-sm">
          <FaIcon :name="item.icon" class="text-3xl" />
        </div>
        <div class="text-center text-sm text-foreground/80 font-medium">
          {{ item.label }}
        </div>
      </div>
    </div>
  </Card>
</template>
