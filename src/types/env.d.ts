interface ImportMetaEnv {
  // Auto generate by env-parse
  /**
   * 项目名称
   */
  readonly VITE_APP_PROJECT_NAME: string
  /**
   * OSS 路径
   */
  readonly VITE_OSS_HOST: string
  /**
   * CDN 路径
   */
  readonly VITE_OSS_CND: string
  /**
   * 应用配置面板
   */
  readonly VITE_APP_SETTING: boolean
  /**
   * 页面标题
   */
  readonly VITE_APP_TITLE: string
  /**
   * 代理接口前缀
   */
  readonly VITE_APP_API_PROXY_PREFIX: string
  /**
   * 接口请求地址，会设置到 axios 的 baseURL 参数上
   */
  readonly VITE_APP_API_BASEURL: string
  /**
   * localStorage/sessionStorage 前缀
   */
  readonly VITE_APP_STORAGE_PREFIX: string
  /**
   * 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
   */
  readonly VITE_APP_DEBUG_TOOL: string
  /**
   * 是否禁用开发者工具，可防止被调试
   */
  readonly VITE_APP_DISABLE_DEVTOOL: boolean
  /**
   * 是否开启代理
   */
  readonly VITE_OPEN_PROXY: boolean
  /**
   * 是否开启开发者工具
   */
  readonly VITE_OPEN_DEVTOOLS: boolean
  /**
   * 环境
   */
  readonly VITE_APP_ENV: string
  /**
   * 应用 SCID
   */
  readonly VITE_APP_SCID: string
  /**
   * 在测试服或正式服登录后，在控制中通过 'localStorage.sc_refresh_token' 获取 value 的值
   */
  readonly VITE_APP_REFRESH_TOKEN: string
}
